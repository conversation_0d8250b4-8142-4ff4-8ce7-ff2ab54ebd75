#!/usr/bin/env python3
"""
关键词监控任务快速测试

这是一个简化的快速测试脚本，用于验证关键词监控功能是否正常工作。
适合开发调试时使用。

使用方法:
python tasks/examples/keyword_monitor_quick_test.py
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from settings.config import settings
from tasks.core.models import TaskConfig
from tasks.monitors.keyword import KeywordMonitorTask
from tasks.core.logger import TaskLogger

from log import logger


async def quick_test():
    """快速测试关键词监控功能"""
    logger.info("🚀 关键词监控快速测试")
    logger.info("=" * 40)
    
    try:
        # 初始化数据库
        logger.info("📡 连接数据库...")
        await Tortoise.init(config=settings.tortoise_orm)
        logger.info("✅ 数据库连接成功")
        
        # 检查关键词数据
        from models.trendinsight import TrendInsightKeyword
        total_count = await TrendInsightKeyword.all().count()
        logger.info(f"📊 数据库中关键词数量: {total_count}")
        
        if total_count == 0:
            logger.warning("⚠️ 没有关键词数据，测试将退出")
            return
        
        # 获取 id = 2 的关键词
        sample_keyword = await TrendInsightKeyword.filter(id=2).first()
        if sample_keyword:
            logger.info(f"📝 示例关键词: {sample_keyword.keyword} (ID: {sample_keyword.id})")
        else:
            logger.warning("⚠️ 未找到 ID=2 的关键词，使用第一个关键词")
            sample_keyword = await TrendInsightKeyword.all().first()
            if sample_keyword:
                logger.info(f"📝 示例关键词: {sample_keyword.keyword} (ID: {sample_keyword.id})")
            else:
                logger.error("❌ 数据库中没有任何关键词")
                return
        
        # 创建简单的测试配置
        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=2,  # 只处理2个关键词
            max_age_hours=168,  # 7天范围，确保有数据
            timeout=60  # 1分钟超时
        )
        
        # 创建任务实例
        task_logger = TaskLogger("quick_test")
        task = KeywordMonitorTask(config, task_logger)
        
        # 执行测试
        logger.info("🔍 开始执行关键词监控测试...")
        start_time = datetime.now()
        
        result = await task.execute()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 显示结果
        logger.info(f"\n📊 测试结果:")
        logger.info(f"   ✅ 状态: {result.status}")
        logger.info(f"   📈 处理数量: {result.processed_count}")
        logger.info(f"   ✅ 成功数量: {result.success_count}")
        logger.info(f"   ❌ 失败数量: {result.failed_count}")
        logger.info(f"   ⏱️ 执行时间: {duration:.2f} 秒")
        
        if result.errors:
            logger.warning(f"   ⚠️ 错误信息: {result.errors[0]}")
        
        # 判断测试结果
        if result.status in ["success", "partial"]:
            logger.info("🎉 快速测试通过！关键词监控功能正常")
        else:
            logger.error("❌ 快速测试失败，请检查配置和数据")
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        
    finally:
        # 关闭数据库连接
        try:
            await Tortoise.close_connections()
            logger.info("✅ 数据库连接已关闭")
        except:
            pass


if __name__ == "__main__":
    asyncio.run(quick_test())
