"""
抖音视频URL解析控制器

提供抖音视频URL解析和aweme_id提取的业务逻辑处理
支持多种URL格式的解析：
- 精选页面URL: https://www.douyin.com/jingxuan?modal_id=7425961429783579941
- PC视频URL: https://www.douyin.com/video/7485337020965948711  
- 短链接URL: https://v.douyin.com/pSKg1x5CLYc/
"""

import logging
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from fastapi import HTTPException

from rpc.douyin.html_handler.constants import DouyinURLConstants
from rpc.douyin.html_handler.schemas import URLType
from rpc.douyin.html_handler.url_manager import DouyinURLManager
from utils.douyin.url_processor import DouyinUrlProcessor


class VideoUrlParseController:
    """抖音视频URL解析控制器"""

    def __init__(self, timeout: int = 10):
        """
        初始化URL解析控制器

        Args:
            timeout: HTTP请求超时时间（秒）
        """
        self.timeout = timeout
        self.url_processor = DouyinUrlProcessor(timeout=timeout)
        self.logger = logging.getLogger(__name__)

    async def parse_video_url(
        self,
        url: str,
        resolve_redirects: bool = True,
        validate_result: bool = True,
    ) -> Dict[str, Any]:
        """
        解析抖音视频URL并提取aweme_id

        Args:
            url: 抖音视频URL
            resolve_redirects: 是否解析重定向（针对短链接）
            validate_result: 是否验证解析结果

        Returns:
            Dict: 解析结果，包含以下字段：
                - success: bool - 解析是否成功
                - aweme_id: Optional[str] - 提取的视频ID
                - url_type: Optional[str] - URL类型
                - original_url: str - 原始URL
                - resolved_url: Optional[str] - 解析后的完整URL（如果是短链接）
                - error: Optional[str] - 错误信息

        Raises:
            HTTPException: 当URL格式无效或解析失败时
        """
        result = {
            "success": False,
            "aweme_id": None,
            "url_type": None,
            "original_url": url,
            "resolved_url": None,
            "error": None,
        }

        try:
            # 输入验证
            if not url or not isinstance(url, str):
                result["error"] = "URL不能为空且必须为字符串"
                raise HTTPException(status_code=400, detail=result["error"])

            url = url.strip()
            if not url:
                result["error"] = "URL不能为空"
                raise HTTPException(status_code=400, detail=result["error"])

            self.logger.info(f"开始解析URL: {url}")

            # 1. 检测URL类型
            url_type = self._detect_url_type(url)
            result["url_type"] = url_type if url_type else "unknown"

            # 2. 处理短链接重定向
            working_url = url
            if url_type == "short" and resolve_redirects:
                try:
                    working_url = self.url_processor.convert_short_url_to_full(url)
                    result["resolved_url"] = working_url
                    self.logger.info(f"短链接解析成功: {url} -> {working_url}")

                    # 重新检测解析后的URL类型
                    url_type = self._detect_url_type(working_url)
                    result["url_type"] = url_type if url_type else "unknown"
                except Exception as e:
                    result["error"] = f"短链接解析失败: {str(e)}"
                    self.logger.error(f"短链接解析失败: {url}, 错误: {str(e)}")
                    raise HTTPException(status_code=400, detail=result["error"])

            # 3. 提取aweme_id
            aweme_id = self._extract_aweme_id(working_url, url_type)
            if not aweme_id:
                result["error"] = f"无法从URL中提取aweme_id: {working_url}"
                self.logger.warning(f"aweme_id提取失败: {working_url}")
                raise HTTPException(status_code=400, detail=result["error"])

            result["aweme_id"] = aweme_id
            self.logger.info(f"aweme_id提取成功: {aweme_id}")

            # 4. 验证结果
            if validate_result and not self._validate_aweme_id(aweme_id):
                result["error"] = f"提取的aweme_id格式无效: {aweme_id}"
                self.logger.warning(f"aweme_id格式验证失败: {aweme_id}")
                raise HTTPException(status_code=400, detail=result["error"])

            result["success"] = True
            self.logger.info(f"URL解析完成: {url} -> aweme_id: {aweme_id}")
            return result

        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"URL解析过程中发生未预期错误: {str(e)}"
            result["error"] = error_msg
            self.logger.error(f"URL解析异常: {url}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=error_msg)

    async def batch_parse_urls(
        self,
        urls: List[str],
        resolve_redirects: bool = True,
        validate_result: bool = True,
        continue_on_error: bool = True,
    ) -> Dict[str, Any]:
        """
        批量解析抖音视频URL

        Args:
            urls: URL列表
            resolve_redirects: 是否解析重定向
            validate_result: 是否验证解析结果
            continue_on_error: 遇到错误时是否继续处理剩余URL

        Returns:
            Dict: 批量解析结果，包含以下字段：
                - success: bool - 是否有成功解析的URL
                - total_count: int - 总URL数量
                - success_count: int - 成功解析数量
                - error_count: int - 解析失败数量
                - results: List[Dict] - 每个URL的解析结果
                - errors: List[Dict] - 错误详情

        Raises:
            HTTPException: 当输入参数无效时
        """
        batch_result = {
            "success": False,
            "total_count": 0,
            "success_count": 0,
            "error_count": 0,
            "results": [],
            "errors": [],
        }

        try:
            # 输入验证
            if not urls or not isinstance(urls, list):
                raise HTTPException(status_code=400, detail="URLs必须为非空列表")

            batch_result["total_count"] = len(urls)
            self.logger.info(f"开始批量解析{len(urls)}个URL")

            for i, url in enumerate(urls):
                try:
                    result = await self.parse_video_url(
                        url=url,
                        resolve_redirects=resolve_redirects,
                        validate_result=validate_result,
                    )
                    batch_result["results"].append(result)
                    if result["success"]:
                        batch_result["success_count"] += 1
                    else:
                        batch_result["error_count"] += 1

                except HTTPException as e:
                    error_info = {
                        "index": i,
                        "url": url,
                        "error": str(e.detail),
                        "status_code": e.status_code,
                    }
                    batch_result["errors"].append(error_info)
                    batch_result["error_count"] += 1

                    if not continue_on_error:
                        raise

                except Exception as e:
                    error_info = {
                        "index": i,
                        "url": url,
                        "error": f"未预期错误: {str(e)}",
                        "status_code": 500,
                    }
                    batch_result["errors"].append(error_info)
                    batch_result["error_count"] += 1

                    if not continue_on_error:
                        raise HTTPException(status_code=500, detail=f"批量解析失败: {str(e)}")

            batch_result["success"] = batch_result["success_count"] > 0
            self.logger.info(
                f"批量解析完成: 总数={batch_result['total_count']}, "
                f"成功={batch_result['success_count']}, "
                f"失败={batch_result['error_count']}"
            )

            return batch_result

        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"批量解析过程中发生未预期错误: {str(e)}"
            self.logger.error(f"批量解析异常: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)

    async def validate_url(self, url: str) -> Dict[str, Any]:
        """
        验证抖音视频URL的有效性

        Args:
            url: 待验证的URL

        Returns:
            Dict: 验证结果，包含以下字段：
                - is_valid: bool - URL是否有效
                - url_type: Optional[str] - URL类型
                - is_douyin_url: bool - 是否为抖音URL
                - can_extract_aweme_id: bool - 是否可以提取aweme_id
                - errors: List[str] - 验证错误列表

        Raises:
            HTTPException: 当输入参数无效时
        """
        validation_result = {
            "is_valid": False,
            "url_type": None,
            "is_douyin_url": False,
            "can_extract_aweme_id": False,
            "errors": [],
        }

        try:
            # 输入验证
            if not url or not isinstance(url, str):
                validation_result["errors"].append("URL不能为空且必须为字符串")
                return validation_result

            url = url.strip()
            if not url:
                validation_result["errors"].append("URL不能为空")
                return validation_result

            self.logger.info(f"开始验证URL: {url}")

            # 1. 基础URL格式验证
            try:
                parsed = urlparse(url)
                if not parsed.scheme or not parsed.netloc:
                    validation_result["errors"].append("URL格式无效：缺少协议或域名")
                    return validation_result
            except Exception as e:
                validation_result["errors"].append(f"URL解析失败: {str(e)}")
                return validation_result

            # 2. 检查是否为抖音URL
            validation_result["is_douyin_url"] = DouyinURLManager.is_douyin_url(url)
            if not validation_result["is_douyin_url"]:
                validation_result["errors"].append("不是抖音URL")
                return validation_result

            # 3. 检测URL类型
            url_type = self._detect_url_type(url)
            validation_result["url_type"] = url_type if url_type else "unknown"

            if not url_type or url_type == "unknown":
                validation_result["errors"].append("不支持的抖音URL类型")
                return validation_result

            # 4. 检查是否可以提取aweme_id
            try:
                aweme_id = self._extract_aweme_id(url, url_type)
                validation_result["can_extract_aweme_id"] = bool(aweme_id)

                if not aweme_id:
                    validation_result["errors"].append("无法从URL中提取aweme_id")
                elif not self._validate_aweme_id(aweme_id):
                    validation_result["errors"].append(f"提取的aweme_id格式无效: {aweme_id}")
                    validation_result["can_extract_aweme_id"] = False

            except Exception as e:
                validation_result["errors"].append(f"aweme_id提取测试失败: {str(e)}")

            # 5. 最终验证结果
            validation_result["is_valid"] = (
                validation_result["is_douyin_url"]
                and validation_result["url_type"] != "unknown"
                and validation_result["can_extract_aweme_id"]
                and not validation_result["errors"]
            )

            self.logger.info(f"URL验证完成: {url}, 有效={validation_result['is_valid']}")
            return validation_result

        except Exception as e:
            error_msg = f"URL验证过程中发生未预期错误: {str(e)}"
            validation_result["errors"].append(error_msg)
            self.logger.error(f"URL验证异常: {url}, 错误: {str(e)}")
            return validation_result

    def _detect_url_type(self, url: str) -> Optional[str]:
        """
        检测URL类型

        Args:
            url: 待检测的URL

        Returns:
            str: URL类型字符串，如果无法识别则返回None
        """
        try:
            parsed = urlparse(url)
            domain = parsed.netloc
            path = parsed.path

            # 短链接
            if domain == DouyinURLConstants.DOMAIN_SHORT:
                return "short"

            # 使用现有的URL管理器检测其他类型
            url_type = DouyinURLManager.get_url_type(url)
            return url_type.value if url_type else None

        except Exception:
            return None

    def _extract_aweme_id(self, url: str, url_type: Optional[str] = None) -> Optional[str]:
        """
        从URL中提取aweme_id

        Args:
            url: 待提取的URL
            url_type: URL类型（可选）

        Returns:
            Optional[str]: 提取的aweme_id，失败返回None
        """
        try:
            # 使用URL管理器提取
            aweme_id = DouyinURLManager.parse_aweme_id_from_url(url)
            if aweme_id:
                return aweme_id

            # 使用URL处理器作为备用方案
            return self.url_processor.extract_video_id(url)
        except Exception:
            return None

    def _validate_aweme_id(self, aweme_id: str) -> bool:
        """
        验证aweme_id格式

        Args:
            aweme_id: 待验证的aweme_id

        Returns:
            bool: 格式是否有效
        """
        try:
            return DouyinURLManager.validate_aweme_id(aweme_id)
        except Exception:
            return False
