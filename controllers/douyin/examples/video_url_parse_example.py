"""
VideoUrlParseController 使用示例

演示如何使用 VideoUrlParseController 解析抖音视频URL并提取aweme_id
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from log import logger

from controllers.douyin.video_url_parse_controller import VideoUrlParseController


async def main():
    """主函数，演示VideoUrlParseController的各种功能"""

    # 创建控制器实例
    controller = VideoUrlParseController(timeout=10)

    logger.info("=== VideoUrlParseController 使用示例 ===\n")

    # 1. 单个URL解析示例
    logger.info("1. 单个URL解析:")
    test_urls = [
        "https://www.douyin.com/jingxuan?modal_id=7425961429783579941",
        "https://www.douyin.com/video/7485337020965948711",
        "https://m.douyin.com/share/video/7123456789012345678",
    ]

    for url in test_urls:
        try:
            result = await controller.parse_video_url(url, resolve_redirects=False)
            logger.info(f"✅ URL: {url}")
            logger.info(f"   aweme_id: {result['aweme_id']}")
            logger.info(f"   url_type: {result['url_type']}")
            logger.info("")
        except Exception as e:
            logger.error(f"❌ URL解析失败: {url}")
            logger.error(f"   错误: {str(e)}\n")

    # 2. 批量URL解析示例
    logger.info("2. 批量URL解析:")
    try:
        batch_result = await controller.batch_parse_urls(test_urls, resolve_redirects=False)
        logger.info(f"✅ 批量解析结果:")
        logger.info(f"   总数: {batch_result['total_count']}")
        logger.info(f"   成功: {batch_result['success_count']}")
        logger.info(f"   失败: {batch_result['error_count']}")

        # 显示成功解析的结果
        for i, result in enumerate(batch_result["results"]):
            if result["success"]:
                logger.info(f"   #{i+1}: {result['aweme_id']} ({result['url_type']})")
        logger.info("")

    except Exception as e:
        logger.error(f"❌ 批量解析失败: {str(e)}\n")

    # 3. URL验证示例
    logger.info("3. URL验证:")
    validation_urls = [
        "https://www.douyin.com/jingxuan?modal_id=7425961429783579941",  # 有效
        "https://v.douyin.com/pSKg1x5CLYc/",  # 短链接
        "https://invalid-domain.com/video/123",  # 无效
        "",  # 空URL
    ]

    for url in validation_urls:
        try:
            result = await controller.validate_url(url)
            status = "✅ 有效" if result["is_valid"] else "❌ 无效"
            logger.info(f"{status}: {url}")
            logger.info(f"   URL类型: {result['url_type']}")
            logger.info(f"   是否抖音URL: {result['is_douyin_url']}")
            logger.info(f"   可提取ID: {result['can_extract_aweme_id']}")
            if result["errors"]:
                logger.info(f"   错误: {', '.join(result['errors'])}")
            logger.info("")
        except Exception as e:
            logger.error(f"❌ 验证失败: {url}")
            logger.error(f"   错误: {str(e)}\n")

    # 4. 短链接处理示例（需要网络请求）
    logger.info("4. 短链接处理示例:")
    short_url = "https://v.douyin.com/pSKg1x5CLYc/"

    logger.info(f"短链接: {short_url}")

    # 验证短链接
    validation = await controller.validate_url(short_url)
    logger.info(f"✅ 短链接验证: 类型={validation['url_type']}, 抖音URL={validation['is_douyin_url']}")

    # 注意：实际解析短链接需要网络请求，这里仅做演示
    logger.info("💡 提示：短链接的完整解析需要网络请求来获取重定向后的URL")
    logger.info("   使用 parse_video_url(url, resolve_redirects=True) 可以自动处理重定向")

    logger.info("\n=== 示例结束 ===")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())