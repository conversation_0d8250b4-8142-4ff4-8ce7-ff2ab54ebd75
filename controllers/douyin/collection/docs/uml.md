# DouyinCollectionController UML 类图

## 1. 核心类图

```mermaid
classDiagram
    class <PERSON>uyinCollectionController {
        +__init__()
        +_get_douyin_client() async DouyinCollectionAPI
        +get_self_aweme_collection_rpc_with_cookies(cursor: int, count: int, cookies: str) async SelfAwemeCollectionResponse
        +get_collect_video_list_rpc_with_cookies(collects_id: str, cursor: int, count: int, cookies: str) async CollectVideoListResponse
        +sync_and_save_single_collection_with_cookies(collection_id: str, cookies: str) async Dict
    }

    class async_douyin_collection_api {
        <<interface>>
        +get_self_aweme_collection(request: SelfAwemeCollectionRequest, cookies: str) async SelfAwemeCollectionResponse
        +get_collect_video_list(request: CollectVideoListRequest, cookies: str) async CollectVideoListResponse
    }

    class CollectionDataMapper {
        <<static>>
        +map_video_info_to_douyin_aweme(video: VideoInfo) static DouyinAwemeData
    }

    class DouyinAweme {
        <<ORM Model>>
        +aweme_id: str
        +desc: str
        +create_time: int
        +user_id: str
        +nickname: str
        +avatar: str
        +cover_url: str
        +video_download_url: str
        +liked_count: int
        +comment_count: int
        +share_count: int
        +aweme_type: int
        +ip_location: str
        +user_signature: str
        +user_unique_id: str
        +sec_uid: str
        +short_user_id: str
        +aweme_url: str
        +source_keyword: str
        +title: str
        +collected_count: int
        +is_deleted: bool
        +created_at: datetime
        +updated_at: datetime
        +filter(aweme_id__in: List[str]) QuerySet
        +bulk_create(objects: List[DouyinAweme]) async
        +all() async List[DouyinAweme]
    }

    DouyinCollectionController --> async_douyin_collection_api : uses
    DouyinCollectionController --> CollectionDataMapper : uses
    DouyinCollectionController --> DouyinAweme : manipulates
    CollectionDataMapper --> DouyinAweme : creates
```

## 2. 请求响应数据模型

```mermaid
classDiagram
    class SelfAwemeCollectionRequest {
        +cursor: int
        +count: int
    }

    class CollectVideoListRequest {
        +collects_id: str
        +cursor: int
        +count: int
    }

    class SelfAwemeCollectionResponse {
        +collects_list: List[CollectionInfo]
        +cursor: int
        +has_more: bool
        +status_code: int
        +status_msg: str
    }

    class CollectVideoListResponse {
        +aweme_list: List[VideoInfo]
        +cursor: int
        +has_more: bool
        +status_code: int
        +status_msg: str
    }

    class CollectionInfo {
        +collects_id: str
        +collects_name: str
        +collects_count: int
        +cover: str
        +create_time: int
        +update_time: int
    }

    class VideoInfo {
        +aweme_id: str
        +desc: str
        +create_time: int
        +author: AuthorInfo
        +statistics: StatisticsInfo
        +video: VideoDetail
        +music: MusicInfo
        +aweme_type: int
        +ip_location: str
    }

    class AuthorInfo {
        +uid: str
        +sec_uid: str
        +short_id: str
        +unique_id: str
        +nickname: str
        +avatar_larger: str
        +signature: str
    }

    class StatisticsInfo {
        +digg_count: int
        +comment_count: int
        +share_count: int
        +collect_count: int
        +play_count: int
    }

    class VideoDetail {
        +play_addr: PlayAddr
        +cover: Cover
        +dynamic_cover: Cover
        +duration: int
        +ratio: str
    }

    SelfAwemeCollectionResponse --> CollectionInfo : contains
    CollectVideoListResponse --> VideoInfo : contains
    VideoInfo --> AuthorInfo : has
    VideoInfo --> StatisticsInfo : has
    VideoInfo --> VideoDetail : has
```

## 3. 数据转换层架构

```mermaid
classDiagram
    class VideoInfo {
        <<RPC Response>>
        +aweme_id: str
        +desc: str
        +create_time: int
        +author: AuthorInfo
        +statistics: StatisticsInfo
        +video: VideoDetail
    }

    class DouyinAwemeData {
        <<Pydantic Model>>
        +aweme_id: str
        +desc: str
        +create_time: int
        +user_id: str
        +nickname: str
        +avatar: str
        +cover_url: str
        +video_download_url: str
        +liked_count: int
        +comment_count: int
        +share_count: int
        +model_dump() Dict
    }

    class DouyinAweme {
        <<Tortoise ORM>>
        +aweme_id: str
        +desc: str
        +create_time: int
        +user_id: str
        +nickname: str
        +avatar: str
        +cover_url: str
        +video_download_url: str
        +liked_count: int
        +comment_count: int
        +share_count: int
        +created_at: datetime
        +updated_at: datetime
        +is_deleted: bool
    }

    VideoInfo --> DouyinAwemeData : mapped by CollectionDataMapper
    DouyinAwemeData --> DouyinAweme : converted to ORM model
```

## 4. 异常处理架构

```mermaid
classDiagram
    class HTTPException {
        +status_code: int
        +detail: str
    }

    class DouyinCollectionController {
        +get_self_aweme_collection_rpc_with_cookies() async
        +get_collect_video_list_rpc_with_cookies() async
        +sync_and_save_single_collection_with_cookies() async
    }

    class ErrorResult {
        +collections_synced: int
        +videos_synced: int
        +collections_filtered: int
        +relations_created: int
        +relations_existing: int
        +trendinsight_relations_created: int
        +trendinsight_relations_existing: int
        +aweme_ids: List[str]
        +video_items: List[VideoItemResponse]
        +errors: List[str]
    }

    DouyinCollectionController --> HTTPException : throws on critical errors
    DouyinCollectionController --> ErrorResult : returns with error details
```

## 5. 依赖关系图

```mermaid
classDiagram
    direction TB
    
    class FastAPIController {
        <<External>>
    }
    
    class DouyinCollectionController {
        <<Controller Layer>>
    }
    
    class async_douyin_collection_api {
        <<RPC Layer>>
    }
    
    class CollectionDataMapper {
        <<Mapping Layer>>
    }
    
    class DouyinAweme {
        <<Data Layer>>
    }
    
    class HTTPException {
        <<Exception>>
    }
    
    class DouyinSchemas {
        <<Schema Layer>>
        SelfAwemeCollectionRequest
        CollectVideoListRequest
        SelfAwemeCollectionResponse
        CollectVideoListResponse
        VideoInfo
    }
    
    class TrendInsightSchemas {
        <<Schema Layer>>
        DouyinAwemeData
        VideoItemResponse
    }

    FastAPIController --> DouyinCollectionController
    DouyinCollectionController --> async_douyin_collection_api
    DouyinCollectionController --> CollectionDataMapper
    DouyinCollectionController --> DouyinAweme
    DouyinCollectionController --> HTTPException
    DouyinCollectionController --> DouyinSchemas
    DouyinCollectionController --> TrendInsightSchemas
    CollectionDataMapper --> DouyinSchemas
    CollectionDataMapper --> TrendInsightSchemas
```

## 6. 实例创建模式

```mermaid
classDiagram
    class DouyinCollectionController {
        +__init__()
    }
    
    class ControllerSingleton {
        -douyin_collection_controller: DouyinCollectionController
        -collection_controller: DouyinCollectionController
    }
    
    note for ControllerSingleton "模块级单例模式\n保持向后兼容性"
    
    ControllerSingleton --> DouyinCollectionController : creates and manages
```

## 关键设计模式

1. **单例模式**: 控制器实例在模块级别创建，确保全局唯一
2. **工厂模式**: `_get_douyin_client()` 方法作为客户端工厂
3. **映射器模式**: `CollectionDataMapper` 处理数据转换
4. **批量处理模式**: 使用 `bulk_create` 进行批量数据库操作
5. **错误聚合模式**: 收集所有错误并在结果中返回

## 架构特点

- **分层架构**: 清晰的控制器、RPC、映射、数据访问层分离
- **异步处理**: 全面使用 async/await 模式
- **数据验证**: 使用 Pydantic 模型进行数据验证
- **ORM 抽象**: 使用 Tortoise ORM 进行数据库操作
- **错误处理**: 完善的异常处理和错误信息收集机制