"""
抖音 Cookies 相关 API 路由

提供需要携带 Cookies 的抖音平台相关 API 接口
"""

from fastapi import APIRouter, Header, Path

from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController
from rpc.douyin.schemas import UserInfoResponse
from schemas.douyin import CookiesValidateResponse
from schemas.responses import STANDARD_RESPONSES

router = APIRouter(
    prefix="/douyin/cookies",
    tags=["抖音 Cookies API"],
)


# 共享的参数定义
COOKIES_HEADER_REQUIRED = Header(
    ...,
    description="抖音网站的Cookie字符串，用于身份验证和访问权限",
    example="sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz",
    min_length=10,
    title="Cookies字符串",
)


@router.post(
    "/validate",
    response_model=CookiesValidateResponse,
    summary="验证Cookies有效性",
    description="""
验证传入的Cookies是否可用

**功能说明：**
- 接收客户端传入的cookies参数
- 调用抖音RPC客户端的pong方法验证cookies有效性
- 返回验证结果，包含有效性状态和详细消息

**验证流程：**
1. 接收客户端传入的cookies字符串
2. 创建抖音RPC客户端实例
3. 调用客户端的pong方法验证cookies
4. 返回验证结果和相应消息

**返回结果：**
- `valid`: 布尔值，表示cookies是否有效
- `message`: 字符串，包含验证结果的详细信息

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/douyin/cookies/validate" \\
     -H "Content-Type: application/json" \\
     -H "cookies: sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz"
```
    """,
    operation_id="validate_cookies",
    tags=["抖音 Cookies API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "验证完成（无论成功或失败都返回200状态码）",
            "model": CookiesValidateResponse,
        },
    },
)
async def validate_cookies(
    cookies: str = COOKIES_HEADER_REQUIRED,
) -> CookiesValidateResponse:
    """验证Cookies有效性"""
    controller = DouyinRPCVideoController()
    return await controller.validate_cookies(cookies)


@router.get(
    "/user-info/{sec_user_id}",
    response_model=UserInfoResponse,
    summary="获取用户信息(RPC响应+传入cookies)",
    description="""
获取抖音用户信息

**功能说明：**
- 接收客户端传入的cookies参数和用户sec_user_id
- 调用抖音RPC客户端获取用户信息
- 返回RPC原响应格式的用户信息

**请求流程：**
1. 接收客户端传入的cookies字符串和用户sec_user_id
2. 创建抖音RPC客户端实例
3. 调用RPC客户端的get_user_info方法
4. 返回RPC原响应格式的用户信息

**返回结果：**
- `status_code`: 状态码，0表示成功
- `user`: 用户信息对象，包含昵称、头像、粉丝数等详细信息

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/douyin/cookies/user-info/MS4wLjABAAAAXXXXXXXXX" \\
     -H "cookies: sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz"
```
    """,
    operation_id="get_user_info_with_cookies",
    tags=["抖音 Cookies API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "获取成功",
            "model": UserInfoResponse,
        },
    },
)
async def get_user_info_with_cookies(
    sec_user_id: str = Path(..., description="用户的加密ID", example="MS4wLjABAAAAXXXXXXXXX"),
    cookies: str = COOKIES_HEADER_REQUIRED,
) -> UserInfoResponse:
    """传入cookies获取用户信息，返回RPC原响应格式"""
    controller = DouyinRPCVideoController()
    response = await controller.get_user_info_with_cookies(sec_user_id, cookies)
    return response
