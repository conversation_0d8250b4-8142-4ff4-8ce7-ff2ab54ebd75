"""
抖音 API 路由

提供抖音平台相关的 API 接口
"""

from typing import Dict

from fastapi import APIRouter, Header, HTTPException, Path, Query
from pydantic import BaseModel

from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController
from controllers.douyin.video.video_process_controller import video_process_controller
from rpc.douyin import async_douyin_api
from rpc.douyin.schemas import DiscoverSearchRequest, DiscoverSearchResponse, VideoDetailResponse
from schemas.douyin import DouyinAwemeResponse, VideoProcessResponse
from schemas.responses import STANDARD_RESPONSES

router = APIRouter(
    prefix="/douyin",
    tags=["抖音 API"],
)


# 共享的参数定义
VIDEO_ID_PARAM = Path(..., description="抖音视频ID", example="7123456789012345678", title="视频ID")

COOKIES_HEADER_REQUIRED = Header(
    ...,
    description="抖音网站的Cookie字符串，用于身份验证和访问权限",
    example="sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz",
    min_length=10,
    title="Cookies字符串",
)

COOKIES_HEADER_OPTIONAL = Header(
    None,
    description="可选的抖音网站Cookie字符串",
    example="sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz",
    title="Cookies字符串",
)


# 请求体模型
class ExtractWebDataRequest(BaseModel):
    """提取网页数据请求模型"""

    url: str

    class Config:
        json_schema_extra = {"example": {"url": "https://www.douyin.com/video/7123456789012345678"}}


@router.get(
    "/video/rpc-auto-cookies/{video_id}",
    response_model=VideoDetailResponse,
    summary="获取视频详情(RPC响应+自动获取cookies)",
    description="不传入cookies，从数据库获取有效cookies，返回RPC原响应格式的视频详情",
    operation_id="get_video_rpc_auto_cookies",
    tags=["抖音 API"],
)
async def get_video_rpc_auto_cookies(
    video_id: str = VIDEO_ID_PARAM,
) -> VideoDetailResponse:
    """自动获取cookies并获取视频详情，返回RPC原响应格式"""
    controller = DouyinRPCVideoController()
    return await controller.get_video_rpc_auto_cookies(video_id)


@router.get(
    "/video/db-auto-cookies/{video_id}",
    response_model=DouyinAwemeResponse,
    summary="获取视频详情(数据库模型+自动获取cookies)",
    description="不传入cookies，从数据库获取有效cookies，返回数据库模型格式的视频详情",
    operation_id="get_video_db_auto_cookies",
    tags=["抖音 API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "获取成功",
            "model": DouyinAwemeResponse,
        },
    },
)
async def get_video_db_auto_cookies(
    video_id: str = VIDEO_ID_PARAM,
) -> DouyinAwemeResponse:
    """自动获取cookies并获取视频详情，返回数据库模型格式"""
    controller = DouyinRPCVideoController()
    return await controller.get_video_db_auto_cookies(video_id)


@router.post(
    "/video/process/{video_id}",
    response_model=VideoProcessResponse,
    summary="处理视频ID",
    description="""
处理视频ID并提取相关数据

**功能说明：**
- 接收视频ID，处理并返回相关数据
- 自动从数据库获取有效cookies
- 返回处理后的视频信息
- 支持多种数据源：数据库 → 移动端URL → 精选 → RPC API

**处理流程：**
1. 首先尝试从数据库获取视频数据
2. 如果数据库中不存在，则通过移动端URL提取
3. 如果移动端提取失败，则尝试精选提取
4. 最后通过RPC API获取数据并保存到数据库

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/douyin/video/process/7123456789012345678" \\
     -H "Content-Type: application/json"
```
    """,
    operation_id="process_video_id",
    tags=["抖音 API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "处理成功",
            "model": VideoProcessResponse,
        },
    },
)
async def process_video_id(
    video_id: str = VIDEO_ID_PARAM,
) -> VideoProcessResponse:
    """处理视频ID"""
    result = await video_process_controller.process_video_id(video_id)
    return VideoProcessResponse(**result)


@router.get(
    "/discover/search",
    response_model=DiscoverSearchResponse,
    summary="发现搜索",
    description="""
发现搜索接口，支持搜索用户、音乐、话题等内容

**功能说明：**
- 支持通过关键词搜索各类内容
- 支持分页查询
- 可选择搜索频道
- 支持传入cookies或自动获取

**参数说明：**
- keyword: 搜索关键词（必需）
- offset: 分页偏移量（默认：0）
- count: 每页数量（默认：20，最大：50）
- search_channel: 搜索频道（可选）
- cookies: Cookie字符串（可选，为空时自动获取）

**使用示例：**
```bash
# 基础搜索
curl "http://localhost:8000/api/v1/douyin/discover/search?keyword=抖音"

# 分页搜索
curl "http://localhost:8000/api/v1/douyin/discover/search?keyword=抖音&offset=20&count=10"

# 带cookies搜索
curl "http://localhost:8000/api/v1/douyin/discover/search?keyword=抖音" \\
     -H "cookies: sessionid=xxx; odin_tt=yyy"
```
    """,
    operation_id="discover_search",
    tags=["抖音 API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "搜索成功",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "type": 0,
                        "user_list": [
                            {
                                "user_info": {
                                    "uid": "1750021778709080",
                                    "nickname": "用户昵称",
                                    "signature": "个性签名",
                                    "follower_count": 1000,
                                    "unique_id": "user_unique_id",
                                }
                            }
                        ],
                        "cursor": 20,
                        "has_more": 1,
                        "input_keyword": "抖音",
                    }
                }
            },
        },
    },
)
async def discover_search(
    keyword: str = Query(..., description="搜索关键词", example="抖音"),
    offset: int = Query(0, description="分页偏移量", ge=0, example=0),
    count: int = Query(20, description="每页数量", ge=1, le=50, example=20),
    search_channel: str = Query(None, description="搜索频道", example="user"),
    cookies: str = COOKIES_HEADER_OPTIONAL,
) -> DiscoverSearchResponse:
    """发现搜索"""
    try:
        client = async_douyin_api
        request = DiscoverSearchRequest(keyword=keyword, offset=offset, count=count, search_channel=search_channel)

        # 使用提供的 cookies，如果没有则传递 None
        response = await client.discover_search(request, cookies)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发现搜索失败: {str(e)}")
